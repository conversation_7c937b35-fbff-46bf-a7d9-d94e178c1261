{{define "subject"}}Welcome to the Greenlight API!{{end}}

{{define "plainBody"}}

Hi,

Thanks for signing up for Greenlight account. We're excited to have you on board!

For future reference, your user ID number is {{.ID}}.

Please send a request to the `PUT /v1/users/activated` endpoint with the
following JSON
body to activate your account:

{"token": "{{.activationToken}}"}

Please note that this is a one-time use token and it will expire in 3 days.

Thanks,

The Greenlight Team
{{end}}

{{define "htmlBody"}}
<!doctype html>
<html>

<head>
  <meta name="viewport" content="width=device-width" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>

<body>
  <p>Hi,</p>
  <p>Thanks for signing up for Greenlight account. We're excited to have you on board!</p>
  <p>For future reference, your user ID number is {{.ID}}.</p>
  <p>Please send a request to the <code>PUT /v1/users/activated</code>
  endpoint with the
  following JSON body to activate your account:</p>
  <pre><code>
  {"token": "{{.activationToken}}"}
  </code></pre>
  <p>Please note that this is a one-time use token and it will expire in 3
  days.</p>
  <p>Thanks,</p>
  <p>The Greenlight Team</p>
</body>

</html>
{{end}}
