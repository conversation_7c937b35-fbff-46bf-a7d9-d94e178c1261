Thank you for contributing to Gomail! Here are a few guidelines:

## Bugs

If you think you found a bug, create an issue and supply the minimum amount
of code triggering the bug so it can be reproduced.


## Fixing a bug

If you want to fix a bug, you can send a pull request. It should contains a
new test or update an existing one to cover that bug.


## New feature proposal

If you think Gomail lacks a feature, you can open an issue or send a pull
request. I want to keep Gomail code and API as simple as possible so please
describe your needs so we can discuss whether this feature should be added to
Gomail or not.
