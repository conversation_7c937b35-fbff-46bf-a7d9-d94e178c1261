language: go

go:
  - 1.14.x
  - 1.15.x
  - master

sudo: true

env:
  global:
    - PGUSER=postgres
    - PQGOSSLTESTS=1
    - PQSSLCERTTEST_PATH=$PWD/certs
    - PGHOST=127.0.0.1
    - GODEBUG=x509ignoreCN=0
  matrix:
    - PGVERSION=10
    - PGVERSION=9.6
    - PGVERSION=9.5
    - PGVERSION=9.4

before_install:
  - ./.travis.sh postgresql_uninstall
  - ./.travis.sh pgdg_repository
  - ./.travis.sh postgresql_install
  - ./.travis.sh postgresql_configure
  - ./.travis.sh client_configure
  - go get golang.org/x/tools/cmd/goimports
  - go get golang.org/x/lint/golint
  - GO111MODULE=on go get honnef.co/go/tools/cmd/staticcheck@2020.1.3

before_script:
  - createdb pqgotest
  - createuser -DRS pqgossltest
  - createuser -DRS pqgosslcert

script:
  - >
    goimports -d -e $(find -name '*.go') | awk '{ print } END { exit NR == 0 ? 0 : 1 }'
  - go vet ./...
  - staticcheck -go 1.13 ./...
  - golint ./...
  - PQTEST_BINARY_PARAMETERS=no  go test -race -v ./...
  - PQTEST_BINARY_PARAMETERS=yes go test -race -v ./...
